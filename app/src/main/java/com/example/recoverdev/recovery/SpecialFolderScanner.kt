package com.example.recoverdev.recovery

import android.content.Context
import android.util.Log
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class SpecialFolderScanner(
    private val context: Context?,
    private val onScanPathUpdate: ((String) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "SpecialFolderScanner"
        
        // Special folder paths
        private val SPECIAL_FOLDERS = mapOf(
            "thumbnails" to listOf(
                "/storage/emulated/0/Android/data/com.android.providers.media/albumthumbs",
                "/storage/emulated/0/DCIM/.thumbnails", 
                "/storage/emulated/0/.thumbnails",
                "/data/data/com.android.providers.media/cache/thumbnails",
                "/sdcard/Android/data/com.android.providers.media/albumthumbs",
                "/storage/emulated/0/Android/data/com.google.android.apps.photos/cache/thumbnails",
                "/storage/emulated/0/Android/data/com.miui.gallery/cache/thumbnails",
                "/storage/emulated/0/Android/data/com.samsung.android.gallery3d/cache/thumbnails",
                "/storage/emulated/0/Pictures/.thumbnails"
            ),
            "cache" to listOf(
                "/storage/emulated/0/Android/data/*/cache",
                "/data/data/*/cache",
                "/storage/emulated/0/.cache",
                "/sdcard/.cache"
            ),
            "temp" to listOf(
                "/storage/emulated/0/.temp",
                "/storage/emulated/0/temp",
                "/data/local/tmp",
                "/cache"
            ),
            "hidden" to listOf(
                "/storage/emulated/0/.*",
                "/sdcard/.*"
            ),
            "recent" to listOf(
                "/storage/emulated/0/Android/data/com.android.providers.media/recent",
                "/data/data/com.android.providers.media/databases"
            ),
            "trash" to listOf(
                "/storage/emulated/0/.Trash",
                "/sdcard/.Trash",
                "/storage/emulated/0/.trash",
                "/sdcard/.trash",
                "/storage/emulated/0/Trash",
                "/sdcard/Trash"
            )
        )
    }
    
    suspend fun scanSpecialFolders(fileType: FileType): List<RecoverableFile> {
        return withContext(Dispatchers.IO) {
            val files = mutableListOf<RecoverableFile>()
            
            try {
                Log.d(TAG, "Starting special folder scan, file type: $fileType")
                
                // Scan thumbnail folders
                files.addAll(scanThumbnailFolders(fileType))
                
                // Scan cache folders
                files.addAll(scanCacheFolders(fileType))
                
                // Scan temporary folders
                files.addAll(scanTempFolders(fileType))
                
                // Scan hidden folders
                files.addAll(scanHiddenFolders(fileType))
                
                // Scan recent files
                files.addAll(scanRecentFiles(fileType))

                // Scan trash folders
                files.addAll(scanTrashFolders(fileType))

                Log.d(TAG, "Special folder scan completed, found ${files.size} files")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to scan special folders", e)
            }
            
            files
        }
    }
    
    private suspend fun scanThumbnailFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["thumbnails"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                Log.d(TAG, "Checking thumbnail folder: $folderPath, exists: ${folder.exists()}, is directory: ${folder.isDirectory}, readable: ${folder.canRead()}")
                
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "Scanning thumbnail folder: $folderPath")
                    val foundFiles = scanFolderRecursively(folder, fileType, "thumbnails")
                    files.addAll(foundFiles)
                    Log.d(TAG, "Found ${foundFiles.size} files in $folderPath")
                } else {
                    Log.d(TAG, "Thumbnail folder does not exist or is not accessible: $folderPath")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Cannot access thumbnail folder: $folderPath", e)
            }
        }
        
        return files
    }
    
    private suspend fun scanCacheFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["cache"]?.forEach { folderPath ->
            try {
                if (folderPath.contains("*")) {
                    // Handle wildcard paths
                    val basePath = folderPath.substringBefore("*")
                    val baseFolder = File(basePath)
                    if (baseFolder.exists() && baseFolder.isDirectory) {
                        baseFolder.listFiles()?.forEach { subFolder ->
                            if (subFolder.isDirectory) {
                                val cachePath = folderPath.replace("*", subFolder.name)
                                val cacheFolder = File(cachePath)
                                if (cacheFolder.exists()) {
                                    files.addAll(scanFolderRecursively(cacheFolder, fileType, "cache"))
                                }
                            }
                        }
                    }
                } else {
                    val folder = File(folderPath)
                    if (folder.exists() && folder.isDirectory) {
                        Log.d(TAG, "Scanning cache folder: $folderPath")
                        files.addAll(scanFolderRecursively(folder, fileType, "cache"))
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "Cannot access cache folder: $folderPath", e)
            }
        }
        
        return files
    }
    
    private suspend fun scanTempFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["temp"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "Scanning temporary folder: $folderPath")
                    files.addAll(scanFolderRecursively(folder, fileType, "temp"))
                }
            } catch (e: Exception) {
                Log.w(TAG, "Cannot access temporary folder: $folderPath", e)
            }
        }
        
        return files
    }
    
    private suspend fun scanHiddenFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            val sdcardPath = "/storage/emulated/0"
            val sdcard = File(sdcardPath)
            if (sdcard.exists() && sdcard.isDirectory) {
                sdcard.listFiles()?.forEach { file ->
                    if (file.isDirectory && file.name.startsWith(".") && file.canRead()) {
                        Log.d(TAG, "Scanning hidden folder: ${file.absolutePath}")
                        files.addAll(scanFolderRecursively(file, fileType, "hidden", maxDepth = 2))
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to scan hidden folders", e)
        }
        
        return files
    }
    
    private suspend fun scanRecentFiles(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["recent"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "Scanning recent folder: $folderPath")
                    files.addAll(scanFolderRecursively(folder, fileType, "recent"))
                }
            } catch (e: Exception) {
                Log.w(TAG, "Cannot access recent folder: $folderPath", e)
            }
        }
        
        return files
    }

    private suspend fun scanTrashFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()

        SPECIAL_FOLDERS["trash"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "Scanning trash folder: $folderPath")
                    files.addAll(scanFolderRecursively(folder, fileType, "trash"))
                }
            } catch (e: Exception) {
                Log.w(TAG, "Cannot access trash folder: $folderPath", e)
            }
        }

        return files
    }

    private suspend fun scanFolderRecursively(
        folder: File,
        fileType: FileType,
        folderCategory: String,
        maxDepth: Int = 3,
        currentDepth: Int = 0
    ): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        if (currentDepth >= maxDepth || !folder.canRead()) {
            return files
        }
        
        try {
            onScanPathUpdate?.invoke("${folder.absolutePath}")
            delay(50)

            folder.listFiles()?.forEach { file ->
                if (file.isFile && isMatchingFileType(file, fileType)) {
                    onScanPathUpdate?.invoke(file.absolutePath)
                    delay(50)

                    val recoverableFile = createRecoverableFile(file, folderCategory)
                    files.add(recoverableFile)
                } else if (file.isDirectory && currentDepth < maxDepth - 1) {
                    files.addAll(scanFolderRecursively(file, fileType, folderCategory, maxDepth, currentDepth + 1))
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to scan folder: ${folder.absolutePath}", e)
        }
        
        return files
    }
    
    private fun isMatchingFileType(file: File, fileType: FileType): Boolean {
        val extension = file.extension.lowercase()
        
        return when (fileType) {
            FileType.PHOTO -> {
                extension in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif")
            }
            FileType.VIDEO -> {
                extension in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts")
            }
            FileType.AUDIO -> {
                extension in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus")
            }
            FileType.OTHER -> {
                extension !in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif",
                                   "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts",
                                   "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus")
            }
        }
    }
    
    private fun createRecoverableFile(file: File, folderCategory: String): RecoverableFile {
        val extension = file.extension.lowercase()
        val fileType = when {
            extension in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif") -> FileType.PHOTO
            extension in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts") -> FileType.VIDEO
            extension in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus") -> FileType.AUDIO
            else -> FileType.OTHER
        }

        // 生成缩略图路径（对于图片和视频文件）
        val thumbnailPath = when (fileType) {
            FileType.PHOTO, FileType.VIDEO -> file.absolutePath
            else -> null
        }

        return RecoverableFile(
            id = "${folderCategory}_${file.absolutePath.hashCode()}_${System.currentTimeMillis()}",
            name = file.name,
            path = file.absolutePath,
            size = file.length(),
            dateModified = file.lastModified(),
            type = fileType,
            format = extension.uppercase(),  // 保持格式一致性
            mimeType = getMimeType(extension),
            isRecovered = false,
            recoveredPath = null,
            thumbnailPath = thumbnailPath  // 添加缩略图路径
        )
    }
    
    private fun getMimeType(extension: String): String {
        return when (extension.lowercase()) {
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            else -> "application/octet-stream"
        }
    }
}