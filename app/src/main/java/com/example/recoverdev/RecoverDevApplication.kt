package com.example.recoverdev

import android.app.Application
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.decode.VideoFrameDecoder
import com.tencent.mmkv.MMKV

class RecoverDevApplication : Application(), ImageLoaderFactory {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize MMKV
        MMKV.initialize(this)
    }
    
    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this)
            .components {
                add(VideoFrameDecoder.Factory())
            }
            .logger(coil.util.DebugLogger())  // 添加调试日志
            .respectCacheHeaders(false)  // 忽略缓存头
            .build()
    }
}