package com.example.recoverdev.ui.components

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.media.ThumbnailUtils
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import android.util.Size
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import coil.request.videoFrameMillis
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import java.io.File
import com.example.recoverdev.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GridFileItem(
    file: RecoverableFile,
    isSelected: Boolean = false,
    isSelectionMode: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
                .aspectRatio(1f)
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
                .background(Color.White, RoundedCornerShape(8.dp))
        ) {
            // Content area
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(6.dp)),
                contentAlignment = Alignment.Center
            ) {
                Log.d("liyq", "File: ${file.name}, thumbnailPath: ${file.thumbnailPath}, recoveredPath: ${file.recoveredPath}, path: ${file.path}")
                Log.d("liyq", "path: ${file.thumbnailPath ?: file.recoveredPath?.ifBlank { file.path } ?: file.path}")
                when (file.type) {
                    FileType.PHOTO -> {
                        // 优先使用缩略图路径，然后是恢复路径，最后是原始路径
                        val imagePath = file.thumbnailPath ?: file.recoveredPath?.ifBlank { file.path } ?: file.path
                        AsyncImage(
                                model = ImageRequest.Builder(LocalContext.current)
                                    .data(imagePath)
                                    .placeholder(R.mipmap.pre_image)
                                    .error(R.mipmap.pre_image)  // 添加错误处理
                                    .crossfade(true)
                                    .size(200)
                                    .build(),
                                contentDescription = "Image Preview",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                    }
                    FileType.VIDEO -> {
                        Box {
                            // 优先使用缩略图路径，然后是恢复路径，最后是原始路径
                            val videoPath = file.thumbnailPath ?: file.recoveredPath?.ifBlank { file.path } ?: file.path
                            VideoThumbnail(
                                file = File(videoPath),
                                contentDescription = "Video thumbnail",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                            Icon(
                                imageVector = Icons.Default.PlayArrow,
                                contentDescription = "Play",
                                tint = Color.White,
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .size(24.dp)
                                    .background(
                                        Color.Black.copy(alpha = 0.6f),
                                        CircleShape
                                    )
                                    .padding(4.dp)
                            )
                        }
                    }
                    FileType.AUDIO -> {
                        Image(
                            painter = painterResource(id = R.mipmap.pre_music),
                            contentDescription = null,
                            modifier = Modifier.size(200.dp)
                        )
                    }
                    else -> {
                        // This shouldn't happen for grid items, but just in case
                        Text(
                            text = "📄",
                            fontSize = 24.sp
                        )
                    }
                }
            }

            // File size display
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .heightIn(min = 24.dp)
                    .background(brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF424242).copy(alpha = 0f),
                            Color(0xFF1D1D1D).copy(alpha = if (file.type == FileType.AUDIO) 0f else 0.7f)
                        )
                    ),RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp))
                    .padding(horizontal = 2.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = FileUtils.formatFileSize(file.size),
                    color = Color.White,
                    fontSize = 10.sp,
                    lineHeight = 14.sp,
                    modifier = Modifier.align(Alignment.CenterStart)
                )

                if (isSelectionMode) {
                    Box(
                        modifier = Modifier
                            .padding(2.dp)
                            .align(Alignment.CenterEnd)
                    ) {
                        if (isSelected) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Selected",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(14.dp)
                                    .background(color = colorResource(R.color.btn_orange), RoundedCornerShape(4.dp))
                            )
                        } else {
                            Box(
                                modifier = Modifier
                                    .size(14.dp)
                                    .background(Color.Black.copy(alpha = 0.4f), RoundedCornerShape(4.dp))
                                    .border(
                                        width = 1.dp,
                                        color = Color.White,
                                        shape = RoundedCornerShape(4.dp)
                                    )
                            )
                        }
                    }
                }
            }
        }

        if (file.type == FileType.AUDIO) {
            Text(
                text = file.name,
                fontSize = 10.sp,
                lineHeight = 14.sp,
                color = colorResource(R.color.first_text_black),
                maxLines = 2,
                fontWeight = FontWeight.SemiBold,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(vertical = 4.dp, horizontal = 2.dp)
            )
        }
    }
}

@Composable
fun VideoThumbnail(
    file: File,
    modifier: Modifier = Modifier,
    contentDescription: String? = "Video thumbnail",
    contentScale: ContentScale = ContentScale.Fit
) {
    val thumbnailBitmap by produceState<Bitmap?>(initialValue = null, key1 = file.path) {
        value = withContext(Dispatchers.IO) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    ThumbnailUtils.createVideoThumbnail(
                        file,
                        Size(400, 400),
                        null // CancellationSignal
                    )
                } else {
                    @Suppress("DEPRECATION")
                    ThumbnailUtils.createVideoThumbnail(
                        file.path,
                        MediaStore.Images.Thumbnails.MINI_KIND
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    thumbnailBitmap?.let { bitmap ->
        Image(
            bitmap = bitmap.asImageBitmap(),
            contentDescription = contentDescription,
            modifier = modifier.fillMaxSize(),
            contentScale = contentScale
        )
    } ?: run {
        Image(
            painter = painterResource(id = R.mipmap.pre_video),
            contentDescription = "Placeholder",
            modifier = modifier.fillMaxSize(),
            contentScale = contentScale
        )
    }
}