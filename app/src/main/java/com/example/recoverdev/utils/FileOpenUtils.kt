package com.example.recoverdev.utils

import android.content.Context
import android.content.Intent
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import com.example.recoverdev.data.model.RecoverableFile
import java.io.File

object FileOpenUtils {
    
    /**
     * Open file
     */
    fun openFile(context: Context, file: RecoverableFile) {
        try {
            // 优先使用恢复路径，然后是原始路径（不使用thumbnailPath，因为那是用于预览的）
            val filePath = file.recoveredPath?.ifBlank { file.path } ?: file.path
            val fileToOpen = File(filePath)

            android.util.Log.d("FileOpenUtils", "Attempting to open file: ${file.name}, path: $filePath, exists: ${fileToOpen.exists()}")

            if (!fileToOpen.exists()) {
                android.util.Log.w("FileOpenUtils", "File does not exist: $filePath")
                return
            }

            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                fileToOpen
            )

            val mimeType = file.mimeType ?: getMimeType(fileToOpen.extension)
            android.util.Log.d("FileOpenUtils", "Opening file with MIME type: $mimeType")

            val genericIntent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            val chooserIntent = Intent.createChooser(genericIntent, "Open with")
            if (chooserIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(chooserIntent)
                android.util.Log.d("FileOpenUtils", "File opened successfully: ${file.name}")
            } else {
                android.util.Log.w("FileOpenUtils", "No app found to open file: ${file.name}")
            }

        } catch (e: Exception) {
            android.util.Log.e("FileOpenUtils", "Failed to open file: ${file.name}", e)
            e.printStackTrace()
        }
    }

    
    /**
     * Get MIME type based on file extension
     */
    private fun getMimeType(extension: String): String {
        return when (extension.lowercase()) {
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "heic" -> "image/heic"
            "mp4" -> "video/mp4"
            "avi" -> "video/avi"
            "mkv" -> "video/mkv"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            "webm" -> "video/webm"
            "3gp" -> "video/3gpp"
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            "aac" -> "audio/aac"
            "ogg" -> "audio/ogg"
            "wma" -> "audio/x-ms-wma"
            "m4a" -> "audio/mp4"
            "pdf" -> "application/pdf"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "txt" -> "text/plain"
            "zip" -> "application/zip"
            "rar" -> "application/x-rar-compressed"
            "apk" -> "application/vnd.android.package-archive"
            else -> {
                MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "application/octet-stream"
            }
        }
    }
}