{"formatVersion": "1.1", "component": {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": "2.0.21", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.8"}}, "variants": [{"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-sources.jar", "size": 1193331, "sha512": "ff246bc6ac75cc58db167ffc7ced1257d6a673cec6d25b7351a9ddfbc0bed00988fc513e08f7e312d2d3667212a4f198bd5c8640dfe7df14818981c641b47807", "sha256": "9f7e04cf93281115c262734f8b5f89cae3bedd85b09a8a92f4123c03a2780b9c", "sha1": "df4f66ae592aa3a82f82b1258fdc402cf9e315f2", "md5": "b04df761ae04cc1ce1b56b54355dfdbe"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}]}, {"name": "runtimeElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21.jar", "url": "kotlin-gradle-plugin-2.0.21.jar", "size": 14959240, "sha512": "e480597150e0579d40fc2298a10160577a777c0859a5974ca689978859af92781aeaea1711d95a9c21be92343f85e4f214c3aba5a3872ed64a5c170c4d6364ec", "sha256": "7c07f7c1545c08984afd4ba504c0eb356032a7cb3181f1952c76f76362ee4358", "sha1": "458703c78df0f8d21c02e219ae1d419a92bd105f", "md5": "12294d0c4f267fd80d0403fab15983f8"}]}, {"name": "apiElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21.jar", "url": "kotlin-gradle-plugin-2.0.21.jar", "size": 14959240, "sha512": "e480597150e0579d40fc2298a10160577a777c0859a5974ca689978859af92781aeaea1711d95a9c21be92343f85e4f214c3aba5a3872ed64a5c170c4d6364ec", "sha256": "7c07f7c1545c08984afd4ba504c0eb356032a7cb3181f1952c76f76362ee4358", "sha1": "458703c78df0f8d21c02e219ae1d419a92bd105f", "md5": "12294d0c4f267fd80d0403fab15983f8"}]}, {"name": "gradle70JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle70-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle70-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle70-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle70-sources.jar", "size": 1211565, "sha512": "8406354854d589aefe412a037baf7733b80e2f824e21aa58a6815c3e6e8431d333c48f2025a257b4b66ee2f20e63bf89033cacfbe659f1bbe1bb1fb449581770", "sha256": "50ab25e699802ec2b0b48a60bafa1f3da5657f0e63af9cfcef4110ea97be5694", "sha1": "d5794b921ec456355037a5f052f22c0f27aa402e", "md5": "d17717f81d62809ab4f3f2dfe0fb90c7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle70.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle70.jar", "size": 14949030, "sha512": "6baf136a00f96ac282498dd8d3daf8f1e193171b36388c52abc936f1141e414d081f9fabff705bfc8ba0f1f0f795892e54b2c4965f1b0a874e8eb9d7195ebdc0", "sha256": "c54a575a64d7b943c2e3a2fdd0c26e7c12cebaec7ab166d55c3ce16e941c76d5", "sha1": "0960f1e69301839624898d2975f7ac29a345edb3", "md5": "e5f699a86027f6db2cef6007271772a5"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle70.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle70.jar", "size": 14949030, "sha512": "6baf136a00f96ac282498dd8d3daf8f1e193171b36388c52abc936f1141e414d081f9fabff705bfc8ba0f1f0f795892e54b2c4965f1b0a874e8eb9d7195ebdc0", "sha256": "c54a575a64d7b943c2e3a2fdd0c26e7c12cebaec7ab166d55c3ce16e941c76d5", "sha1": "0960f1e69301839624898d2975f7ac29a345edb3", "md5": "e5f699a86027f6db2cef6007271772a5"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle71-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle71-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle71-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle71-sources.jar", "size": 1210170, "sha512": "db6ce27412fe475423aed3f252ec7ce43396afde783d4a762b82f41ef378e0c6699f28d36566fc095a964211c2510bead1d20e7efd86b5c60a9b0c27cb267813", "sha256": "1a2a905b1702659d61808a9c1701197e8bc747218fb217ee46d39b3f3bbc87e5", "sha1": "bb84dc0e4180009ef7a3185fe9c4b6a18b938895", "md5": "be36c993d649014fe6952bd7fa76868b"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle71.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle71.jar", "size": 14944183, "sha512": "231c9c822d783b33d0abdda0c8c736891ffec40401f22db6247e1c2d3b0978cb97a0d44256a329e5d72f95742cd01d827d45d61d739ac13f1f32b8e6a14a1b67", "sha256": "8b0cf5f928371bd60445d49be24a2e168087191c2e701c0e05b4092093a20aaf", "sha1": "ed8e23f0902cd9136de9a836eea0acc97aab14cc", "md5": "87542adcb2374dfcb513f092ec511278"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle71.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle71.jar", "size": 14944183, "sha512": "231c9c822d783b33d0abdda0c8c736891ffec40401f22db6247e1c2d3b0978cb97a0d44256a329e5d72f95742cd01d827d45d61d739ac13f1f32b8e6a14a1b67", "sha256": "8b0cf5f928371bd60445d49be24a2e168087191c2e701c0e05b4092093a20aaf", "sha1": "ed8e23f0902cd9136de9a836eea0acc97aab14cc", "md5": "87542adcb2374dfcb513f092ec511278"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle74-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle74-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle74-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle74-sources.jar", "size": 1207481, "sha512": "74288a41418374a3b655eb878e4b2903328d85bfa5726aa04f30bd66df38dcbe1468870ccc63f756f50a583166163c018078769ddd3dc4d9b9a1979983463161", "sha256": "9b705203eb825ae3a5676480ff6a770743d7c046c084f026ae22119f57434280", "sha1": "8f9dfb0a271bb50d713e0cc6e1115c7ae58a55af", "md5": "c294738f710d28f592ac131218354f00"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle74.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle74.jar", "size": 14932723, "sha512": "f447f60625a588977a439b5212ff725e9cf09f870383966a5ec7c32feaf726cce744dc61066305a45c63247aae297c74b274d611386aa1f58da7b24b7802ffed", "sha256": "c6b8e9c17a9d917e76bbfb046a7e2f1c55574d6e722a33dde774b02aaa90ffeb", "sha1": "be33da249a18c9efdd95af97ea814aa15f453138", "md5": "762b81360d9a5b7999c6c94e12f08ead"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle74.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle74.jar", "size": 14932723, "sha512": "f447f60625a588977a439b5212ff725e9cf09f870383966a5ec7c32feaf726cce744dc61066305a45c63247aae297c74b274d611386aa1f58da7b24b7802ffed", "sha256": "c6b8e9c17a9d917e76bbfb046a7e2f1c55574d6e722a33dde774b02aaa90ffeb", "sha1": "be33da249a18c9efdd95af97ea814aa15f453138", "md5": "762b81360d9a5b7999c6c94e12f08ead"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle75-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle75-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle75-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle75-sources.jar", "size": 1205867, "sha512": "9eb625a45b529c3fa0d2623213331ef16c9cef42eba4819cfd39fd51bdb50e03470ee6a8b2d335b82f252ab8895427baa8c965d3fcf7b8d68cf73efc214e51d1", "sha256": "d0f7b508158e954997429d7c10234bb8af5327bab130a6cd70a67c1625d06c11", "sha1": "9c57fa906c6717730ff3d8e97112d9cf576332c9", "md5": "2b1bb294165727189aee7c7bc90d6a78"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle75.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle75.jar", "size": 14925697, "sha512": "2370c0dc8fb2f0651c7412ac66cce48e67120edf3e03234bce4181398b0ea91c7b53e2ccd751c2ccf55b28f7a1954d017483e0845a02ec0e976c26dfa4bee314", "sha256": "fde0cf6305171f3b2e61728f6583b647cc9d6e12d882fdde3efbafe29d4372b7", "sha1": "904d38b786ab5fc577c362534cc888afca2de5a6", "md5": "390f9f673f773650795dc6241391b702"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle75.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle75.jar", "size": 14925697, "sha512": "2370c0dc8fb2f0651c7412ac66cce48e67120edf3e03234bce4181398b0ea91c7b53e2ccd751c2ccf55b28f7a1954d017483e0845a02ec0e976c26dfa4bee314", "sha256": "fde0cf6305171f3b2e61728f6583b647cc9d6e12d882fdde3efbafe29d4372b7", "sha1": "904d38b786ab5fc577c362534cc888afca2de5a6", "md5": "390f9f673f773650795dc6241391b702"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle76-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle76-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle76-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle76-sources.jar", "size": 1204891, "sha512": "35bb7f00b062fd6384ba90a71d06a285810d84c8f45cb405834a2940a4c821a1c29063b0a685cdc402b9735e2e1ef48ddc0465c8a630faeb19a1164ddfc02e22", "sha256": "69e7f14c42659b0f4412271dedbc2393559d7863130fc68aa9c30a8e59a0b8c4", "sha1": "1c66e34d2a8ca3ab5937bc7bde627ab616ace95c", "md5": "df5513ab8c0573ef2f41d07db16e4707"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle76.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle76.jar", "size": 14920289, "sha512": "99dcc287ff47a62308968756cf51aa9369a54cb6fdb423ec97445d79302e3b7ecff26ad13f8167169f383468d9005b0822a55b12f939a79e016d0cd72efb7f71", "sha256": "bcd29b3c9ae0954fae77eabc9fcd7ce4053ddddfc5c04e95123720370442b256", "sha1": "82fa95e473126c11dc4ed5e541ddf92b3180fda4", "md5": "727f4d05618303ab6b3d0655faf80c2c"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle76.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle76.jar", "size": 14920289, "sha512": "99dcc287ff47a62308968756cf51aa9369a54cb6fdb423ec97445d79302e3b7ecff26ad13f8167169f383468d9005b0822a55b12f939a79e016d0cd72efb7f71", "sha256": "bcd29b3c9ae0954fae77eabc9fcd7ce4053ddddfc5c04e95123720370442b256", "sha1": "82fa95e473126c11dc4ed5e541ddf92b3180fda4", "md5": "727f4d05618303ab6b3d0655faf80c2c"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle80-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle80-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle80-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle80-sources.jar", "size": 1202659, "sha512": "7111b41a597f2cc914642103c94300ac6ebbb8be1a13a52ffc13f9856f16d0d2612053aa455851ebaa02ad30c8b58937c93c4f553b7206b132b9b1ff9f2a9375", "sha256": "05a3f2ae71debd723a7221c04d04e8bf1fbc2e65ba59bf19d4791bbf57c6da4e", "sha1": "fb2eaca0414593817af41c2efca9c0144ee626e6", "md5": "2ad267b7cf933400f80a9c97565fa388"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle80.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle80.jar", "size": 14914440, "sha512": "cc8ffc45f9aad44c128f1bee9a2675ac28c9c64fa06568a456cec92a37d0d88499d8b4eab2267184b71934a13b90ec54bca117237010e661dfe4ced030917dcd", "sha256": "79b8c91789cd0e9173f40e28ee7b7fecfd0aa3df7edf047df9d05b5ef334b90f", "sha1": "c724eea81347792285ad3ff407d77e93d6cb5079", "md5": "277e194aee606b9aa04b07468155c611"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle80.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle80.jar", "size": 14914440, "sha512": "cc8ffc45f9aad44c128f1bee9a2675ac28c9c64fa06568a456cec92a37d0d88499d8b4eab2267184b71934a13b90ec54bca117237010e661dfe4ced030917dcd", "sha256": "79b8c91789cd0e9173f40e28ee7b7fecfd0aa3df7edf047df9d05b5ef334b90f", "sha1": "c724eea81347792285ad3ff407d77e93d6cb5079", "md5": "277e194aee606b9aa04b07468155c611"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle81-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle81-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle81-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle81-sources.jar", "size": 1202662, "sha512": "7e923e026695779f98f054f9502c12586be8eae449a0522c288838e3ba2f32d80e995bc76a43797968a1aac085ee77800da05c630ae5c4273ecc8a5129a8d817", "sha256": "525859fa0f5d16830717f04e935c7f53b9a64a570bf0d51d619f90c160595b9b", "sha1": "e3d425a88257e999fc51bdb54b024a0b6a85ea78", "md5": "2c52fc7894ebe4a4aa1addb611209d2c"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle81.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle81.jar", "size": 14914446, "sha512": "50b93cc293385fcfbf54b74eafde0b841fd1be685b89a5f33fdc1519f81b7a85354d6cd3e7a52a41e7cd7b549c03308b9aad62f8811d5c125c436b27257b8444", "sha256": "c2377d25ce11000daeaf89cafc75fcc4dd1154186b9169bfd8d6ce1a5dbb9d5b", "sha1": "c9d2c6e23c5d9f9934896d8fe5e528a5dcfbcfa6", "md5": "f78f50de6c457b5ce7baae4d348c755a"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle81.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle81.jar", "size": 14914446, "sha512": "50b93cc293385fcfbf54b74eafde0b841fd1be685b89a5f33fdc1519f81b7a85354d6cd3e7a52a41e7cd7b549c03308b9aad62f8811d5c125c436b27257b8444", "sha256": "c2377d25ce11000daeaf89cafc75fcc4dd1154186b9169bfd8d6ce1a5dbb9d5b", "sha1": "c9d2c6e23c5d9f9934896d8fe5e528a5dcfbcfa6", "md5": "f78f50de6c457b5ce7baae4d348c755a"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle82-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle82-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle82-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle82-sources.jar", "size": 1202016, "sha512": "8b78e07f8b9aa61692cbf5abfcc44c15be41725c2f4acc2836c39f70c7fb4511f9c964b578a0df0a004a7de3ff5e8fa1cabf63f4548ecfee584a0e9cc8cffbbd", "sha256": "5f28cfccdf965a6c56004d34cb31cbdd3c532f6c9c63b9d17892dbd23f350c11", "sha1": "7fd8c83038d51b9955cc1dd300c9f64acdd28603", "md5": "c0cc7fa857e6d9ba8dacee949269c33d"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle82.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle82.jar", "size": 14912382, "sha512": "28e34f840fb24f503c074e3947a27cc083e1febe67ec67537843e37dbae7bcc59b70b8d4c7521d198b20fcf9888a9710f1562d97654756f687a049d0e59a4880", "sha256": "e85db8a3df5ee63bbb9cd1d29d09ad2f8574e5bdcc0cac902aa3029398cd5d04", "sha1": "824ee26231720b4edd1f0009bfdbe03a0fa63c3a", "md5": "8f98e0f3ff4d0daf6c077fb89d37925c"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle82.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle82.jar", "size": 14912382, "sha512": "28e34f840fb24f503c074e3947a27cc083e1febe67ec67537843e37dbae7bcc59b70b8d4c7521d198b20fcf9888a9710f1562d97654756f687a049d0e59a4880", "sha256": "e85db8a3df5ee63bbb9cd1d29d09ad2f8574e5bdcc0cac902aa3029398cd5d04", "sha1": "824ee26231720b4edd1f0009bfdbe03a0fa63c3a", "md5": "8f98e0f3ff4d0daf6c077fb89d37925c"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle85-javadoc.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle85-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle85-sources.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle85-sources.jar", "size": 1200560, "sha512": "7f30d8a83a7c8342b5665689b72baafaf0c6d3a58ee002099925b501a8ef86a82e78aebb2bd5b0e34982f3864e96b7532dfdb5523a5e807ace7aeac18b98e1ed", "sha256": "fa3a2746bff0d91a0bbc6861c33dc3b982e2180247fb933aa0945ae41af241fe", "sha1": "61f6cf1e41a26190a04366febe1967a81d459f68", "md5": "3046e9cd89ca75d8294827de3b767846"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle85.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle85.jar", "size": 14904556, "sha512": "443bd372bfd4aca3728400e2c2269620f4ceda70b1259bd4dc3c7aa155015175511a5a3c206f2281a7a7d9a265a44e3ade38e6b4d7520774aabe2a02f871e5a7", "sha256": "9df5c7ff13b1fc68ac9450ca63c53111891d6f647bddec0f4398b3e7225bf6d9", "sha1": "ea69d8de091115a98ce7f3901994b2fb3432480b", "md5": "cb72ff428ac0ed819cab4fd55bbf0a54"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-klib-commonizer-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-tools-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-statistics", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "<PERSON><PERSON><PERSON>-compiler-runner", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-build-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-util-klib", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-compiler-embeddable", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "kotlin-gradle-plugin-2.0.21-gradle85.jar", "url": "kotlin-gradle-plugin-2.0.21-gradle85.jar", "size": 14904556, "sha512": "443bd372bfd4aca3728400e2c2269620f4ceda70b1259bd4dc3c7aa155015175511a5a3c206f2281a7a7d9a265a44e3ade38e6b4d7520774aabe2a02f871e5a7", "sha256": "9df5c7ff13b1fc68ac9450ca63c53111891d6f647bddec0f4398b3e7225bf6d9", "sha1": "ea69d8de091115a98ce7f3901994b2fb3432480b", "md5": "cb72ff428ac0ed819cab4fd55bbf0a54"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin", "version": "2.0.21"}]}]}