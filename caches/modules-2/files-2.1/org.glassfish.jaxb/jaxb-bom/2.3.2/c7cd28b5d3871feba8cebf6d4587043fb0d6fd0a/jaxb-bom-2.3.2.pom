<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>jaxb-bom</artifactId>
    <version>2.3.2</version>

    <packaging>pom</packaging>
    <name>JAXB BOM</name>
    <description>JAXB Bill of Materials (BOM)</description>

    <properties>
        <jaxb-api.version>2.3.2</jaxb-api.version>
        <jaxb-api-osgi.version>2.3</jaxb-api-osgi.version>
        <istack.version>3.0.8</istack.version>
        <fastinfoset.version>1.2.16</fastinfoset.version>
        <stax-ex.version>1.8.1</stax-ex.version>
        <dtd-parser.version>1.4.1</dtd-parser.version>
        <codemodel.version>2.3.2</codemodel.version>
        <relaxng.version>2.3.2</relaxng.version>
        <xsom.version>2.3.2</xsom.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- *** SOURCES *** -->
            <!-- new -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>

            <!--OLD-->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>

            <dependency> <!--JAXB-API-->
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jaxb-api.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.jvnet.staxex</groupId>
                <artifactId>stax-ex</artifactId>
                <version>${stax-ex.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.fastinfoset</groupId>
                <artifactId>FastInfoset</artifactId>
                <version>${fastinfoset.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.dtd-parser</groupId>
                <artifactId>dtd-parser</artifactId>
                <version>${dtd-parser.version}</version>
            </dependency>


            <!-- *** BINARIES *** -->
            <!-- new -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--OLD-->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- OSGI -->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-osgi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Dependencies -->

            <dependency> <!--JAXB-API-->
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-runtime</artifactId>
                <version>${istack.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.fastinfoset</groupId>
                <artifactId>FastInfoset</artifactId>
                <version>${fastinfoset.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jvnet.staxex</groupId>
                <artifactId>stax-ex</artifactId>
                <version>${stax-ex.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>1.2.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
