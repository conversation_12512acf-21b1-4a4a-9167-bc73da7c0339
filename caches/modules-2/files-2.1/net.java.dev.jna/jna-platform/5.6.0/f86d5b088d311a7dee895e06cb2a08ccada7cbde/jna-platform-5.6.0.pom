<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
  http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>net.java.dev.jna</groupId>
  <artifactId>jna-platform</artifactId>
  <version>5.6.0</version>
  <packaging>jar</packaging>

  <name>Java Native Access Platform</name>
  <description>Java Native Access Platform</description>
  <url>https://github.com/java-native-access/jna</url>

  <licenses>
      <license>
          <name>LGPL, version 2.1</name>
          <url>http://www.gnu.org/licenses/licenses.html</url>
          <distribution>repo</distribution>
      </license>
      <license>
          <name>Apache License v2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
          <distribution>repo</distribution>
      </license>
  </licenses>

  <scm>
    <connection>scm:git:https://github.com/java-native-access/jna</connection>
    <developerConnection>scm:git:ssh://**************/java-native-access/jna.git</developerConnection>
    <url>https://github.com/java-native-access/jna</url>
  </scm>

  <developers>
    <developer>
      <id>twall</id>
      <name>Timothy Wall</name>
      <roles>
        <role>Owner</role>
      </roles>
    </developer>
    <developer>
      <email><EMAIL></email>
      <name>Matthias Bläsing</name>
      <url>https://github.com/matthiasblaesing/</url>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <dependencies>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.6.0</version>
    </dependency>
  </dependencies>

</project>
