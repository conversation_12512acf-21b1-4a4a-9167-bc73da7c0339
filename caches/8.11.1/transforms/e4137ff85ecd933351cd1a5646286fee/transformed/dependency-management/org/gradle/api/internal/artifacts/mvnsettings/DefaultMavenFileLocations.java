/*
 * Copyright 2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.api.internal.artifacts.mvnsettings;

import org.gradle.util.internal.MavenUtil;

import javax.annotation.Nullable;
import java.io.File;

public class DefaultMavenFileLocations implements MavenFileLocations {
    @Override
    public File getUserMavenDir() {
        return MavenUtil.getUserMavenDir();
    }

    @Override
    @Nullable
    public File getGlobalMavenDir() {
        return MavenUtil.getGlobalMavenDir();
    }

    @Override
    public File getUserSettingsFile() {
        return new File(getUserMavenDir(), "settings.xml");
    }

    @Override
    @Nullable
    public File getGlobalSettingsFile() {
        File dir = getGlobalMavenDir();
        if (dir == null) {
            return null;
        }
        return new File(dir, "conf/settings.xml");
    }
}
