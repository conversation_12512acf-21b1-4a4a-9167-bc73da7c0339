/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact;

/**
 * A container of the artifacts visited during graph traversal.
 */
public interface VisitedArtifactSet {
    /**
     * Creates a set that selects the artifacts from this set that match the given criteria.
     * Implementations are lazy, so that the selection happens only when the contents are queried.
     *
     * @param spec Parameters controlling the artifact selection process
     */
    SelectedArtifactSet select(ArtifactSelectionSpec spec);
}
