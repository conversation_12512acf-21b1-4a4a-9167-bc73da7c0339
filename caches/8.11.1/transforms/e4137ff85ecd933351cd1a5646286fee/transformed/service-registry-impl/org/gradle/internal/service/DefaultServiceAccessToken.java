/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.service;

class DefaultServiceAccessToken implements ServiceAccessToken {

    private final int id;
    private final String ownerDisplayName;

    public DefaultServiceAccessToken(int id, String ownerDisplayName) {
        this.id = id;
        this.ownerDisplayName = ownerDisplayName;
    }

    @Override
    public final boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DefaultServiceAccessToken)) {
            return false;
        }

        DefaultServiceAccessToken that = (DefaultServiceAccessToken) o;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return id;
    }

    @Override
    public String toString() {
        return "AccessToken(id=" + id + ", owner=" + ownerDisplayName + ")";
    }
}
